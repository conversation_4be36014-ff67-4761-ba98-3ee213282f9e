{"name": "w<PERSON><PERSON>-stays", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start --clear", "android": "expo start --android --clear", "ios": "expo start --ios --clear", "web": "expo start --web --clear", "watch": "watchman watch src --foreground --logfile=/dev/stdout", "build:android": "eas build --platform android --local"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.1", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/devtools": "^7.0.15", "@react-navigation/drawer": "^7.1.2", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.66.7", "axios": "^1.7.9", "axios-retry": "^4.5.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "expo": "53.0.19", "expo-av": "~15.1.7", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-status-bar": "~2.2.3", "expo-video": "~2.2.2", "firebase": "^11.3.1", "i18next": "^24.2.2", "lodash": "^4.17.21", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.79.5", "react-native-blob-util": "^0.21.2", "react-native-country-picker-modal": "^2.0.0", "react-native-edge-to-edge": "1.6.2", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-chat": "^2.8.0", "react-native-international-phone-number": "^0.9.0", "react-native-keyboard-controller": "^1.17.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.2.0", "react-native-onboarding-swiper": "^1.3.0", "react-native-pdf": "^6.7.7", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-smooth-pincode-input": "github:ymdkit/react-native-smooth-pincode-input", "react-native-webview": "13.13.5", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "^5.66.1", "@types/react": "~19.0.10", "react-native-dotenv": "^3.4.11", "reactotron-react-native": "^5.1.14", "reactotron-react-native-mmkv": "^0.2.8", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "@types/react": "~19.0.10"}, "private": true}