import 'dotenv/config';

// Define the environment variables we expect
interface Env {
  GOOGLE_MAPS_API_KEY: string;
  API_BASE_URL: string;
  MMKV_KEY_DEV: string;
  GOOGLE_MAPS_API_KEY_IOS: string;
}

// Cast process.env to our interface
const env = process.env as unknown as Env;

export default {
  expo: {
      name: "wodaabe-stays",
      slug: "wodaabe-stays",
      version: "1.0.0",
      orientation: "portrait",
      icon: "./assets/icon-wodaabe.png",
      userInterfaceStyle: "light",
      owner: "fsli-group",
      newArchEnabled: true,
      githubUrl: "https://github.com/fsli-group/wodaabe-app",
      splash: {
        image: "./assets/icon-wodaabe.png",
        resizeMode: "contain",
        backgroundColor: "#FFFFFF"// "#2c3e50"
      },
      ios: {
        bundleIdentifier: "com.wodaabe-stays.ios",
        supportsTablet: true,
        infoPlist: {
          ITSAppUsesNonExemptEncryption: false,
          NSCameraUsageDescription: "This app needs access to the camera",
          NSPhotoLibraryUsageDescription: "This app needs access to photos",
          UIBackgroundModes: ["remote-notification"],
        },
        excludeXcodeSymbols: true,
        config: {
          googleMapsApiKey: env.GOOGLE_MAPS_API_KEY_IOS,
        },
      },
      android: {
        package: "com.wodaabe_stays",
        googleServicesFile: "./google-services.json",
        permissions: [
          "NOTIFICATIONS",
          "android.permission.RECEIVE_BOOT_COMPLETED",
          "android.permission.VIBRATE"
        ],
        blockedPermissions: [
          "android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK",
          "android.permission.FOREGROUND_SERVICE",
          "android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"
        ],
        config: {
          usesNonExemptEncryption: false
        }
      },
      plugins: [
        "expo-font",
        "expo-localization",
        "react-native-edge-to-edge",
        [
          "expo-video",
          {
            "supportsBackgroundPlayback": false,
            "supportsPictureInPicture": false
          }],
        "./google-maps-plugin.js",
        "./remove-permissions-plugin.js",
        [
          "expo-notifications",
          {
            "icon": "./assets/icon-wodaabe.png",
            "color": "#FFFFFF",
            "sounds": ["./assets/sounds/notification_sound.mp3"],
            "androidMode": "default",
            "androidCollapsedTitle": "Wodaabe Stays",
            "iosDisplayInForeground": true,
          }
        ]
      ],
      extra: {
        eas: {
          projectId: "8a914e15-4b4b-4c51-8c12-d8812a55f996"
        },
        MMKV_KEY: env.MMKV_KEY_DEV,
        API_BASE_URL: env.API_BASE_URL || "https://api.wodaabe-stays.com/api/v1",
        googleMapsApiKey: env.GOOGLE_MAPS_API_KEY,
      }
    },
  }
