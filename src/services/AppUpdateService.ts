import { Platform } from 'react-native';
import SpInAppUpdates, {
  NeedsUpdateResponse,
  IAUUpdateKind,
  StartUpdateOptions,
} from 'sp-react-native-in-app-updates';
import Constants from 'expo-constants';

export interface UpdateInfo {
  hasUpdate: boolean;
  currentVersion?: string;
  storeVersion?: string;
  updateRequired?: boolean;
  releaseNotes?: string;
  other?: any;
}

export interface UpdateOptions {
  // Android options
  updateType?: IAUUpdateKind;
  
  // iOS options
  title?: string;
  message?: string;
  buttonUpgradeText?: string;
  buttonCancelText?: string;
  forceUpgrade?: boolean;
  country?: string;
}

class AppUpdateService {
  private inAppUpdates: SpInAppUpdates;
  private isDebug: boolean;

  constructor(isDebug: boolean = __DEV__) {
    this.isDebug = isDebug;
    this.inAppUpdates = new SpInAppUpdates(isDebug);
  }

  /**
   * Check if there's an update available
   */
  async checkForUpdate(): Promise<UpdateInfo> {
    try {
      const currentVersion = Constants.expoConfig?.version || '1.0.0';
      
      if (this.isDebug) {
        console.log('AppUpdateService: Checking for updates...', {
          currentVersion,
          platform: Platform.OS
        });
      }

      const result: NeedsUpdateResponse = await this.inAppUpdates.checkNeedsUpdate({
        curVersion: currentVersion
      });

      if (this.isDebug) {
        console.log('AppUpdateService: Update check result:', result);
      }

      return {
        hasUpdate: result.shouldUpdate,
        currentVersion,
        storeVersion: result.storeVersion,
        updateRequired: false, // Can be customized based on business logic
        other: result.other
      };
    } catch (error) {
      console.error('AppUpdateService: Error checking for updates:', error);
      return {
        hasUpdate: false,
        currentVersion: Constants.expoConfig?.version || '1.0.0'
      };
    }
  }

  /**
   * Start the update process
   */
  async startUpdate(options: UpdateOptions = {}): Promise<void> {
    try {
      if (this.isDebug) {
        console.log('AppUpdateService: Starting update process...', {
          platform: Platform.OS,
          options
        });
      }

      const updateOptions: StartUpdateOptions = Platform.select({
        android: {
          updateType: options.updateType || IAUUpdateKind.FLEXIBLE,
        },
        ios: {
          title: options.title || 'Update Available',
          message: options.message || 'There is a new version of the app available. Would you like to update?',
          buttonUpgradeText: options.buttonUpgradeText || 'Update',
          buttonCancelText: options.buttonCancelText || 'Later',
          forceUpgrade: options.forceUpgrade || false,
          country: options.country,
        },
      }) as StartUpdateOptions;

      await this.inAppUpdates.startUpdate(updateOptions);
      
      if (this.isDebug) {
        console.log('AppUpdateService: Update process started successfully');
      }
    } catch (error) {
      console.error('AppUpdateService: Error starting update:', error);
      throw error;
    }
  }

  /**
   * Install downloaded update (Android only)
   */
  async installUpdate(): Promise<void> {
    if (Platform.OS === 'android') {
      try {
        await this.inAppUpdates.installUpdate();
        if (this.isDebug) {
          console.log('AppUpdateService: Update installation started');
        }
      } catch (error) {
        console.error('AppUpdateService: Error installing update:', error);
        throw error;
      }
    } else {
      console.warn('AppUpdateService: installUpdate() is only available on Android');
    }
  }

  /**
   * Add status update listener (Android only)
   */
  addStatusUpdateListener(callback: (status: any) => void): void {
    if (Platform.OS === 'android') {
      this.inAppUpdates.addStatusUpdateListener(callback);
    }
  }

  /**
   * Remove status update listener (Android only)
   */
  removeStatusUpdateListener(callback: (status: any) => void): void {
    if (Platform.OS === 'android') {
      this.inAppUpdates.removeStatusUpdateListener(callback);
    }
  }
}

// Export singleton instance
export const appUpdateService = new AppUpdateService();
export default AppUpdateService;
