import React, { useCallback, useLayoutEffect, useState, useRef, useEffect } from 'react';
import { debounce } from 'lodash';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  FlatList,
  Modal,
  Alert,
  ActivityIndicator,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
// Use require instead of import to fix TypeScript error
const HostImage = require('../../../assets/Host.jpg');
import { useVideoPlayer, VideoView } from 'expo-video';
import Animated, {
  useAnimatedStyle,
  withTiming,
  withSequence,
  useSharedValue,
  SlideInDown,
  SlideOutDown
} from 'react-native-reanimated';
import Pdf from 'react-native-pdf';
import { MaterialIcons, Ionicons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { httpClient } from '@/utils/http';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import PointsOfInterestPreview from '@/components/PointsOfInterestPreview';
import CohostInvitationCard from '@/components/CohostInvitationCard';
import { useTranslation } from 'react-i18next';
import { Video, ResizeMode } from 'expo-av';
import FAQDisplaySection from '@/components/FAQDisplaySection';
import { useAuthStore } from '@stores/useAuthStore'

// Define types for the component props and state
interface Media {
  id: string;
  type: string;
  url: string;
  description?: string;
  title?: string;
  amenityTag?: string;
  tutorial?: boolean;
}

interface Amenity {
  id: string;
  icon: {
    library: 'MaterialIcons' | 'Ionicons' | 'FontAwesome5' | 'MaterialCommunityIcons';
    name: string;
    color?: string;
  };
  title: {
    en: string;
    fr: string;
  };
}

interface POI {
  name: string;
  address: string;
  placeId?: string;
  types?: string[];
  photo?: string;
}

interface CohostInvitation {
  uuid: string;
  status: 'pending' | 'accepted' | 'rejected';
  coHost: {
    uuid: string;
    displayName: string;
    email: string;
  };
  createdAt: string;
}

interface FAQ {
  id?: number;
  uuid: string;
  question: string;
  answer: string;
}

interface Listing {
  uuid: string;
  title: string;
  description: string;
  coverImage: string;
  media: Media[];
  amenities: string[];
  pointsOfInterest: POI[];
  'co-host-invitations'?: CohostInvitation[];
  'co-hosts'?: CoHost[];
  host?: {
    uuid: string;
    displayName: string;
  };
  faqs?: FAQ[];
  [key: string]: any;
}

interface ServerResponse {
  code: number;
  status: string;
  data: any;
}

interface InlinePlayerProps {
  media: Media;
  onClose: () => void;
}

interface CoHost {
  uuid: string;
  displayName: string;
}

interface CoHostCardProps {
  cohost: CoHost;
  onRemove: (cohostUuid: string) => Promise<void>;
  housingUuid: string;
}

// CoHostCard component for displaying and managing cohosts
const CoHostCard = ({ cohost, onRemove, housingUuid }: CoHostCardProps) => {
  const [removing, setRemoving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [contacting, setContacting] = useState(false);
  const cardScale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { setConversationId } = useAuthStore();

  // Animated styles for the card
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }],
    opacity: opacity.value
  }));

  // Handle removing a cohost
  const handleRemove = async () => {
    try {
      // Start loading state and animation
      setRemoving(true);
      cardScale.value = withTiming(0.98, { duration: 100 });

      // Call the remove function
      await onRemove(cohost.uuid);

      // Show success state
      setRemoving(false);
      setSuccess(true);

      // Success animation
      cardScale.value = withSequence(
        withTiming(1.05, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );

      // Animate out the card
      setTimeout(() => {
        opacity.value = withTiming(0, { duration: 300 });
        cardScale.value = withTiming(0.8, { duration: 300 });
      }, 500);
    } catch (error) {
      console.error('Error removing cohost:', error);
      // Reset animation on error
      setRemoving(false);
      cardScale.value = withTiming(1, { duration: 200 });
    }
  };

  // Handle contacting a cohost
  const handleContact = async () => {
    try {
      setContacting(true);
      const response = await httpClient.post(`/chat/contact-cohost/${cohost.uuid}/${housingUuid}`) as any;
      if (response.data.conversationId) {
        setConversationId(response.data.conversationId);
        // Navigate to HostChat tab first, then to HostChatRoom
        navigation.navigate('HostTabs', {
          screen: 'HostChat',
          params: {
            screen: 'HostChatRoom',
            params: {
              conversationId: response.data.conversationId
            }
          }
        });
      }
    } catch (error) {
      console.error('Error initiating conversation:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_details_screen.contact_error')
      );
    } finally {
      setContacting(false);
    }
  };

  return (
    <Animated.View style={[styles.cohostCard, cardAnimatedStyle]}>
      <View style={styles.cohostInfo}>
        <View style={styles.cohostAvatar}>
          <Text style={styles.cohostInitial}>{cohost.displayName.charAt(0).toUpperCase()}</Text>
        </View>
        <Text style={styles.cohostName}>{cohost.displayName}</Text>
      </View>
      <View style={styles.cohostActions}>
        <TouchableOpacity
          style={[styles.contactButton, contacting && styles.contactButtonLoading]}
          onPress={handleContact}
          disabled={contacting}
        >
          {contacting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="chat" size={18} color="#fff" style={styles.contactButtonIcon} />
              <Text style={styles.contactButtonText}>{t('common.accommodation_details_screen.contact')}</Text>
            </>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.removeButton, removing && styles.removeButtonLoading, success && styles.removeButtonSuccess]}
          onPress={handleRemove}
          disabled={removing || success}
        >
          {removing ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : success ? (
            <MaterialIcons name="check" size={20} color="#fff" />
          ) : (
            <>
              <MaterialIcons name="person-remove" size={18} color="#fff" style={styles.removeButtonIcon} />
              <Text style={styles.removeButtonText}>{t('common.accommodation_details_screen.remove_cohost')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

// InlinePlayer component that uses expo-video's API
const InlinePlayer = ({ media, onClose }: InlinePlayerProps) => {
  // Create a player instance using the URL as the source.
  const player = useVideoPlayer(media.url, (player) => {
    // Optionally adjust the player – for example, auto-play.
    player.play();
  });

  return (
    <Animated.View
      entering={SlideInDown}
      exiting={SlideOutDown}
      style={styles.inlinePlayerWrapper}
    >
      <View style={styles.inlinePlayerContainer}>
        <View style={styles.inlinePlayerHeader}>
          <Text style={styles.inlinePlayerTitle}>{media.title || media.description}</Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.inlinePlayerClose}>×</Text>
          </TouchableOpacity>
        </View>
        <VideoView
          player={player}
          nativeControls
          style={
            media.type === 'video'
              ? styles.inlineVideoPlayer
              : styles.inlineAudioPlayer
          }
        />
      </View>
    </Animated.View>
  );
};

const AccommodationDetailsScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { listingUuid } = route.params as { listingUuid: string };
  const queryClient = useQueryClient();
  const { t, i18n } = useTranslation();
  const { setConversationId } = useAuthStore();

  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // For PDF media (opened in a modal)
  const [pdfMedia, setPdfMedia] = useState<Media | null>(null);
  const [mediaModalVisible, setMediaModalVisible] = useState(false);

  // For image preview
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<Media | null>(null);

  // For audio/video, we show an inline animated player
  const [inlineMedia, setInlineMedia] = useState<Media | null>(null);
  const [selectedAmenity, setSelectedAmenity] = useState<string | null>(null);

  // New state variables for cohost invitation
  const [showInviteInput, setShowInviteInput] = useState(false);
  const [cohostEmail, setCohostEmail] = useState('');
  const [isEmailValid, setIsEmailValid] = useState<boolean | null>(null); // null means not validated yet
  const [showValidation, setShowValidation] = useState(false); // Only show validation after certain actions
  const [invitationStatus, setInvitationStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Debounced validation function
  const debouncedValidate = useCallback(
    debounce((email: string) => {
      if (email.length > 0) {
        setIsEmailValid(validateEmail(email));
      } else {
        setIsEmailValid(null); // Reset validation for empty field
      }
    }, 800), // 800ms delay
    []
  );
  const inviteButtonScale = useSharedValue(1);

  const scrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);
  const inviteSectionRef = useRef<View>(null);

  // Fetch listing details using React Query
  // State to track if current user is the owner
  const [isOwner, setIsOwner] = useState<boolean>(false);
  const [currentUserUuid, setCurrentUserUuid] = useState<string>('');

  // Fetch user info to determine if current user is the owner
  const getUserInfo = async () => {
    try {
      const userInfo = await httpClient.get<{ data: { uuid: string } }>('users/authenticate/info');
      console.log("Current user info:", userInfo);
      if (userInfo && userInfo.data && userInfo.data.uuid) {
        setCurrentUserUuid(userInfo.data.uuid);
      }
      return userInfo;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  // Fetch user info when component mounts
  useEffect(() => {
    getUserInfo();
  }, []);

  // Fetch listing details using React Query
  const { data: listing, isLoading, error } = useQuery({

    queryKey: ['listing', listingUuid],
    queryFn: async () => {
      const response = await httpClient.get(`/housings/getone/${listingUuid}`) as ServerResponse;
      console.log("Retrieved listing: ", response.data);
      return response.data as Listing;
    },
    enabled: !!listingUuid,
  });

  // Check if current user is the owner when listing data is available
  useEffect(() => {
    if (listing && currentUserUuid) {
      // Check if the current user is the owner of the listing
      const ownerUuid = listing.host?.uuid;
      console.log("Listing owner UUID:", ownerUuid);
      console.log("Current user UUID:", currentUserUuid);
      setIsOwner(ownerUuid === currentUserUuid);
    }
  }, [listing, currentUserUuid]);

  // Get carousel media (non-tutorial images and videos)
  const handleCarouselMedia = () => {
    if (!listing) return [];
    let media = [{ id: 'cover', type: 'image', url: listing.coverImage }];
    listing.media.forEach((medium: Media) => {
      if ((medium.type === 'image' || medium.type === 'video') && !medium.tutorial) {
        media.push(medium);
      }
    });
    return media;
  }

  // Get media for selected amenity or tutorial media
  const getFilteredMedia = () => {
    if (!listing) return [];

    if (selectedAmenity) {
      // Return media with matching amenityTag
      return listing.media.filter((medium: Media) =>
        medium.amenityTag === selectedAmenity
      );
    } else {
      // Return tutorial media with no amenityTag
      return listing.media.filter((medium: Media) =>
        medium.tutorial && !medium.amenityTag
      );
    }
  };

  // Handle amenity selection
  const handleAmenityPress = (amenityId: string) => {
    setSelectedAmenity(selectedAmenity === amenityId ? null : amenityId);
  };

  const carouselMedia: Media[] = handleCarouselMedia();
  const filteredMedia = getFilteredMedia();

  // Helper function to render amenity icons
  const renderAmenityIcon = (amenity: Amenity, size: number = 24, overrideColor?: string) => {
    const { library, name, color } = amenity.icon;
    const iconColor = overrideColor || color || '#333';

    switch (library) {
      case 'MaterialIcons':
        return <MaterialIcons name={name as any} size={size} color={iconColor} />;
      case 'Ionicons':
        return <Ionicons name={name as any} size={size} color={iconColor} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={name as any} size={size} color={iconColor} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons name={name as any} size={size} color={iconColor} />;
      default:
        return <MaterialIcons name="help-outline" size={size} color={iconColor} />;
    }
  };

  // Professional amenities with vector icons
  const amenities: Amenity[] = [
    { id: 'wifi', icon: { library: 'MaterialIcons', name: 'wifi', color: '#4CAF50' }, title: { en: 'Wi-Fi', fr: 'Wi-Fi' } },
    { id: 'parking', icon: { library: 'MaterialIcons', name: 'local-parking', color: '#2196F3' }, title: { en: 'Parking', fr: 'Parking' } },
    { id: 'air_conditioning', icon: { library: 'MaterialIcons', name: 'ac-unit', color: '#00BCD4' }, title: { en: 'Air Conditioning', fr: 'Climatisation' } },
    { id: 'heating', icon: { library: 'MaterialCommunityIcons', name: 'radiator', color: '#FF5722' }, title: { en: 'Heating', fr: 'Chauffage' } },
    { id: 'kitchen', icon: { library: 'MaterialIcons', name: 'kitchen', color: '#795548' }, title: { en: 'Equipped Kitchen', fr: 'Cuisine équipée' } },
    { id: 'tv', icon: { library: 'MaterialIcons', name: 'tv', color: '#9C27B0' }, title: { en: 'TV', fr: 'Télévision' } },
    { id: 'washing_machine', icon: { library: 'MaterialCommunityIcons', name: 'washing-machine', color: '#607D8B' }, title: { en: 'Washing Machine', fr: 'Machine à laver' } },
    { id: 'dryer', icon: { library: 'MaterialCommunityIcons', name: 'tumble-dryer', color: '#9E9E9E' }, title: { en: 'Dryer', fr: 'Sèche-linge' } },
    { id: 'dishwasher', icon: { library: 'MaterialCommunityIcons', name: 'dishwasher', color: '#3F51B5' }, title: { en: 'Dishwasher', fr: 'Lave-vaisselle' } },
    { id: 'pool', icon: { library: 'MaterialCommunityIcons', name: 'pool', color: '#00BCD4' }, title: { en: 'Pool', fr: 'Piscine' } },
    { id: 'hot_tub', icon: { library: 'MaterialCommunityIcons', name: 'hot-tub', color: '#FF9800' }, title: { en: 'Hot Tub', fr: 'Jacuzzi' } },
    { id: 'gym', icon: { library: 'MaterialCommunityIcons', name: 'dumbbell', color: '#F44336' }, title: { en: 'Gym', fr: 'Salle de sport' } },
    { id: 'workspace', icon: { library: 'MaterialIcons', name: 'work', color: '#673AB7' }, title: { en: 'Workspace', fr: 'Espace de travail' } },
    { id: 'balcony', icon: { library: 'MaterialCommunityIcons', name: 'balcony', color: '#4CAF50' }, title: { en: 'Balcony / Terrace', fr: 'Balcon / Terrasse' } },
    { id: 'garden', icon: { library: 'MaterialCommunityIcons', name: 'tree', color: '#4CAF50' }, title: { en: 'Garden', fr: 'Jardin' } },
    { id: 'fireplace', icon: { library: 'MaterialCommunityIcons', name: 'fireplace', color: '#FF5722' }, title: { en: 'Fireplace', fr: 'Cheminée' } },
    { id: 'bbq', icon: { library: 'MaterialCommunityIcons', name: 'grill', color: '#795548' }, title: { en: 'BBQ Area', fr: 'Espace barbecue' } },
    { id: 'safe', icon: { library: 'MaterialIcons', name: 'security', color: '#607D8B' }, title: { en: 'Safe', fr: 'Coffre-fort' } },
    { id: 'elevator', icon: { library: 'MaterialIcons', name: 'elevator', color: '#9E9E9E' }, title: { en: 'Elevator', fr: 'Ascenseur' } },
    { id: 'pet_friendly', icon: { library: 'MaterialIcons', name: 'pets', color: '#FF9800' }, title: { en: 'Pet Friendly', fr: 'Animaux acceptés' } },
    { id: 'leisure_equipment', icon: { library: 'MaterialCommunityIcons', name: 'gamepad-variant', color: '#E91E63' }, title: { en: 'Leisure Equipment', fr: 'Équipements de loisirs' } },
    { id: 'comfort_equipment', icon: { library: 'MaterialCommunityIcons', name: 'sofa', color: '#8BC34A' }, title: { en: 'Comfort Equipment', fr: 'Équipements de confort' } },
    { id: 'rules', icon: { library: 'MaterialIcons', name: 'rule', color: '#FF5722' }, title: { en: 'Rules', fr: 'Règles' } }
  ];

  const handleMediaPress = (media: Media) => {
    if (media.type === 'pdf' || media.type === 'document') {
      setPdfMedia(media);
      setMediaModalVisible(true);
    } else if (media.type === 'image') {
      setSelectedImage(media);
      setImagePreviewVisible(true);
    } else {
      setInlineMedia(media);
    }
  };





  // Render carousel item
  const renderCarouselItem = ({ item }: { item: Media }) => {
    // Find amenity name if amenityTag exists
    const amenity = item.amenityTag ? amenities.find(a => a.id === item.amenityTag) : null;

    return (
      <View style={styles.carouselItemContainer}>
        {item.type === 'video' ? (
          <Video
            source={{ uri: item.url }}
            style={styles.carouselImage}
            resizeMode={ResizeMode.COVER}
            shouldPlay={false}
            isLooping={false}
            useNativeControls
          />
        ) : (
          <Image source={{ uri: item.url }} style={styles.carouselImage} />
        )}

        {/* Image count indicator */}
        <View style={styles.imageCountIndicator}>
          <Text style={styles.imageCountText}>
            {carouselMedia.indexOf(item) + 1}/{carouselMedia.length}
          </Text>
        </View>

        {/* Amenity chip */}
        {amenity && (
          <View style={styles.amenityChip}>
            <View style={styles.amenityChipContent}>
              {renderAmenityIcon(amenity, 16, '#fff')}
              <Text style={styles.amenityChipText}>
                {amenity.title[i18n.language as 'en' | 'fr']}
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderDotIndicator = () => (
    <View style={styles.dotContainer}>
      {carouselMedia.map((_, index) => (
        <View
          key={index}
          style={[
            styles.dot,
            {
              backgroundColor: index === activeImageIndex
                ? '#fff'
                : 'rgba(255, 255, 255, 0.5)',
            },
          ]}
        />
      ))}
    </View>
  );

  const renderMediaItem = ({ item }: { item: Media }) => {
    console.log('Rendering media item:', item);

    // Get icon based on media type
    const getMediaIcon = () => {
      switch(item.type) {
        case 'video': return <MaterialIcons name="videocam" size={24} color="#FF5252" />;
        case 'audio': return <MaterialIcons name="audiotrack" size={24} color="#448AFF" />;
        case 'pdf': return <MaterialIcons name="picture-as-pdf" size={24} color="#FF7043" />;
        case 'document': return <MaterialIcons name="description" size={24} color="#26A69A" />;
        default: return null;
      }
    };

    return (
      <Animated.View>
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => handleMediaPress(item)}
          activeOpacity={0.7}
        >
          <View style={styles.mediaIconContainer}>
            {item.type === 'image' ? (
              <Image
                source={{ uri: item.url }}
                style={styles.mediaThumbnail}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.mediaIconWrapper}>
                {getMediaIcon()}
              </View>
            )}


          </View>
          <Text style={styles.mediaText} numberOfLines={2} ellipsizeMode="tail">
            {item.description || t('common.media.no_description')}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  useFocusEffect(
    useCallback(() => {
      queryClient.invalidateQueries({ queryKey: ['listing', listingUuid] });
    }, [queryClient, listingUuid])
  );

  const handleBackPress = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate('AccommodationScreen');
    }
  }, [navigation]);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          style={[styles.headerButton, styles.backButton]}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
      ),
      headerTitle: listing?.title || t('common.accommodation_details_screen.title'),
      headerRight: () => (
        <TouchableOpacity
          style={[styles.headerButton, styles.optionsButton]}
          onPress={() => navigation.navigate('AccommodationUpdate', { listingUuid: listing?.uuid })}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Text style={styles.optionsButtonText}>{t('common.accommodation_details_screen.update')}</Text>
        </TouchableOpacity>
      ),
      headerStyle: styles.headerStyle,
      headerTitleStyle: styles.headerTitleStyle,
      headerShadowVisible: false,
    });
  }, [navigation, listing?.title, listing?.uuid, t, handleBackPress, isOwner]);

  // State for contact host button animation and loading state
  const [isContactingHost, setIsContactingHost] = useState(false);
  const contactHostButtonScale = useSharedValue(1);

  // State for leave cohost button animation and loading state
  const [isLeavingCohost, setIsLeavingCohost] = useState(false);
  const leaveButtonScale = useSharedValue(1);

  const contactHostButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: contactHostButtonScale.value }]
  }));

  const leaveButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: leaveButtonScale.value }]
  }));

  // Handle contacting the host
  const handleContactHost = async () => {
    if (!listing?.host?.uuid) {
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_details_screen.contact_error')
      );
      return;
    }

    try {
      setIsContactingHost(true);
      contactHostButtonScale.value = withTiming(0.95, { duration: 100 });

      const response = await httpClient.post(`/chat/contact-cohost/${listing.host.uuid}/${listingUuid}`) as any;

      if (response.data.conversationId) {
        setConversationId(response.data.conversationId);

        // Success animation
        contactHostButtonScale.value = withSequence(
          withTiming(1.1, { duration: 200 }),
          withTiming(1, { duration: 200 })
        );

        // Navigate to HostChat tab first, then to HostChatRoom
        navigation.navigate('HostTabs', {
          screen: 'HostChat',
          params: {
            screen: 'HostChatRoom',
            params: {
              conversationId: response.data.conversationId
            }
          }
        });
      }
    } catch (error) {
      console.error('Error initiating conversation with host:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_details_screen.contact_error')
      );

      // Reset animation on error
      contactHostButtonScale.value = withTiming(1, { duration: 200 });
    } finally {
      setIsContactingHost(false);
    }
  };

  // Handle leaving as a cohost
  const handleLeaveCohost = () => {
    Alert.alert(
      t('common.accommodation_details_screen.leave_cohost_title'),
      t('common.accommodation_details_screen.leave_cohost_confirmation'),
      [
        {
          text: t('common.buttons.cancel'),
          style: "cancel"
        },
        {
          text: t('common.buttons.confirm'),
          style: "destructive",
          onPress: async () => {
            try {
              // Start animation and loading state
              setIsLeavingCohost(true);
              leaveButtonScale.value = withTiming(0.95, { duration: 100 });

              // Call the API
              const res = await httpClient.delete(`/housings/${listingUuid}/leave-cohost`) as unknown as ServerResponse;
              console.log("Leave cohost response:", res);

              if (res.code === 200) {
                // Success animation
                leaveButtonScale.value = withSequence(
                  withTiming(1.1, { duration: 200 }),
                  withTiming(1, { duration: 200 })
                );

                // Show success message and navigate back
                setTimeout(() => {
                  Alert.alert(
                    t('common.accommodation_details_screen.leave_cohost_success_title'),
                    t('common.accommodation_details_screen.leave_cohost_success_message')
                  );
                  navigation.goBack();
                }, 500);
              } else {
                // Reset animation and show error
                leaveButtonScale.value = withTiming(1, { duration: 200 });
                setIsLeavingCohost(false);
                Alert.alert("Error", res.data?.message || t('common.accommodation_details_screen.leave_cohost_error'));
              }
            } catch (error) {
              console.error("Error leaving as cohost:", error);
              // Reset animation and show error
              leaveButtonScale.value = withTiming(1, { duration: 200 });
              setIsLeavingCohost(false);
              Alert.alert(
                t('common.errors.title'),
                t('common.accommodation_details_screen.leave_cohost_error')
              );
            }
          }
        }
      ]
    );
  };

  const handleDeleteListing = () => {
    Alert.alert(
      t('common.accommodation_details_screen.delete_listing'),
      t('common.accommodation_details_screen.delete_confirmation'),
      [
        {
          text: t('common.accommodation_details_screen.cancel'),
          style: "cancel"
        },
        {
          text: t('common.accommodation_details_screen.delete'),
          style: "destructive",
          onPress: async () => {
            try {
              const res = await httpClient.delete(`/housings/delete/${listingUuid}`) as unknown as ServerResponse;
              console.log(res);
              if (res.code === 200 && res.status === 'success') {
                Alert.alert("Success", t('common.accommodation_details_screen.delete_success'));
              } else if (res.code === 200 && res.status === '400') {
                Alert.alert("Error", res.data.message);
              }
              navigation.goBack();
            } catch (error) {
              console.error("Error deleting listing:", error);
              Alert.alert("Error", t('common.accommodation_details_screen.delete_error'));
            }
          }
        }
      ]
    );
  };

  const handleInviteCohost = async () => {
    if (!showInviteInput) {
      setShowInviteInput(true);
      return;
    }

    if (!cohostEmail) {
      setShowValidation(true);
      Alert.alert(t('common.errors.title'), t('common.accommodation_details_screen.email_required'));
      return;
    }

    // Validate email format
    const isValid = validateEmail(cohostEmail);
    setIsEmailValid(isValid);
    setShowValidation(true); // Show validation feedback

    if (!isValid) {
      Alert.alert(t('common.errors.title'), t('common.accommodation_details_screen.invalid_email'));
      return;
    }

    setInvitationStatus('loading');
    try {
      try {
        const response = await httpClient.post(`/housings/${listingUuid}/invite-cohost`, {
          email: cohostEmail
        }) as { code: number; data?: { message?: string } };

        if (response.code === 200) {
          setInvitationStatus('success');
          // Animate success state using reanimated
          inviteButtonScale.value = withSequence(
            withTiming(1.2, { duration: 200 }),
            withTiming(1, { duration: 200 })
          );

          // Immediately refetch the listing data to show the new invitation
          queryClient.invalidateQueries({ queryKey: ['listing', listingUuid] });

          // Reset after 700ms
          setTimeout(() => {
            setInvitationStatus('idle');
            setShowInviteInput(false);
            setCohostEmail('');
          }, 700);
        } else {
          throw new Error(response.data?.message || t('common.accommodation_details_screen.invite_error'));
        }
      } catch (error: any) {
        // The HTTP interceptor will handle most errors, but we need to handle the 404 case specifically
        // We need to check if this is a 404 with the specific message
        console.log('Error response:', error.response?.data);
        if (error.response?.status === 404 &&
            (error.response?.data?.data?.message === "User not found or not a host" ||
             error.response?.data?.message === "User not found or not a host")) {
          // Show a specific error message for this case
          setInvitationStatus('error');
          Alert.alert(
            t('common.errors.title'),
            t('common.accommodation_details_screen.user_not_found_or_not_host')
          );
          // Keep the input field visible so the user can correct the email
          setTimeout(() => {
            setInvitationStatus('idle');
            // Focus the input field again so the user can correct the email
            inputRef.current?.focus();
          }, 2000);
          return; // Exit early to prevent the generic error message
        }
        // Re-throw the error to be caught by the outer catch block
        throw error;
      }
    } catch (error) {
      console.error('Error inviting cohost:', error);
      setInvitationStatus('error');
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_details_screen.invite_error')
      );
      setTimeout(() => setInvitationStatus('idle'), 2000);
    }
  };

  // Add animated style for the invite button
  const inviteButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: inviteButtonScale.value }]
  }));

  // Handle input focus to scroll to the input field
  const handleInputFocus = () => {
    // Scroll directly to the bottom of the screen with a small delay
    // to ensure the keyboard is fully shown
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  // Handle removing a cohost
  const handleRemoveCohost = async (cohostUuid: string): Promise<void> => {
    try {
      // Call the API to remove the cohost
      const response = await httpClient.delete(`/housings/${listingUuid}/cohost/${cohostUuid}`) as ServerResponse;
      console.log("Remove cohost response:", response);

      if (response.code === 200) {
        // Refetch the listing data to update the UI
        queryClient.invalidateQueries({ queryKey: ['listing', listingUuid] });
        return Promise.resolve();
      } else {
        Alert.alert(
          t('common.errors.title'),
          t('common.accommodation_details_screen.remove_cohost_error')
        );
        return Promise.reject(new Error('Failed to remove cohost'));
      }
    } catch (error) {
      console.error('Error removing cohost:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_details_screen.remove_cohost_error')
      );
      return Promise.reject(error);
    }
  };

  // Handle deleting a cohost invitation
  const handleDeleteInvitation = async (invitationUuid: string) => {
    try {
      // Optimistically update the UI
      if (listing && listing['co-host-invitations']) {
        const updatedInvitations = listing['co-host-invitations'].filter(
          (invitation: CohostInvitation) => invitation.uuid !== invitationUuid
        );

        // Update the listing in the cache
        queryClient.setQueryData(['listing', listingUuid], {
          ...listing,
          'co-host-invitations': updatedInvitations
        });
      }
    } catch (error) {
      console.error('Error handling invitation deletion:', error);
      // Refetch the listing to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['listing', listingUuid] });
    }
  };

  console.log('filteredMedia', filteredMedia);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
        <Text style={styles.loadingText}>{t('common.accommodation_details_screen.loading')}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('common.accommodation_details_screen.error')}</Text>
      </View>
    );
  }

  if (!listing) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('common.accommodation_details_screen.not_found')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Media Carousel */}
          <View style={styles.carouselContainer}>
            <FlatList
              data={carouselMedia}
              renderItem={renderCarouselItem}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={(event) => {
                const slideIndex = Math.floor(
                  event.nativeEvent.contentOffset.x /
                  event.nativeEvent.layoutMeasurement.width
                );
                setActiveImageIndex(slideIndex);
              }}
              keyExtractor={(item) => item.id || item.url}
            />
            {renderDotIndicator()}
          </View>

          <View style={styles.detailsContainer}>
            <Text style={styles.title}>
              {listing.title}
            </Text>

            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.sectionTitle}>{t('common.accommodation_details_screen.about_accommodation')}</Text>
                <View style={styles.sectionTitleDecoration} />
              </View>
              <Text style={styles.description}>
                {listing.description}
              </Text>
            </View>

            {/* Amenities Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{t('common.accommodation_details_screen.amenities')}</Text>
              <View style={styles.amenitiesContainer}>
                {amenities.map((amenity) => {
                  const hasMedia = listing.media.some((medium: Media) =>
                    medium.amenityTag === amenity.id
                  );
                  if (!hasMedia) return null;

                  return (
                    <TouchableOpacity
                      key={amenity.id}
                      style={[
                        styles.amenityItem,
                        selectedAmenity === amenity.id && styles.selectedAmenity
                      ]}
                      onPress={() => handleAmenityPress(amenity.id)}
                    >
                      <View style={[
                        styles.amenityIconContainer,
                        selectedAmenity === amenity.id && styles.selectedAmenityIconContainer
                      ]}>
                        {renderAmenityIcon(
                          amenity,
                          28,
                          selectedAmenity === amenity.id ? '#fff' : amenity.icon.color
                        )}
                      </View>
                      <Text style={styles.amenityText}>{amenity.title[i18n.language as 'en' | 'fr']}</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Media Section */}
            {filteredMedia.length > 0 && (
              <View style={styles.sectionContainer}>
                <View style={styles.sectionHeaderContainer}>
                  <Text style={styles.sectionTitle}>
                    {selectedAmenity
                      ? `${t('common.accommodation_details_screen.medias')} ${amenities.find(a => a.id === selectedAmenity)?.title[i18n.language as 'en' | 'fr']}`
                      : t('common.accommodation_details_screen.medias')
                    }
                  </Text>
                  <View style={styles.sectionTitleDecoration} />
                </View>
                <View style={styles.mediaGalleryContainer}>
                  <FlatList
                    data={filteredMedia}
                    renderItem={renderMediaItem}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.mediaListContent}
                  />
                </View>
              </View>
            )}

            {/* Inline Player for Audio/Video */}
            {inlineMedia && (
              <InlinePlayer
                media={inlineMedia}
                onClose={() => setInlineMedia(null)}
              />
            )}

            {/* POI List Preview */}
            <PointsOfInterestPreview
              listing={listing}
              onPOIPress={() => {}}
            />

            {/* FAQ Section */}
            {listing.faqs && listing.faqs.length > 0 && (
              <View style={styles.sectionContainer}>
                <FAQDisplaySection faqs={listing.faqs} />
              </View>
            )}

            {/* Cohosts List - Only visible to the owner */}
            {isOwner && listing?.['co-hosts'] && (
              <View style={styles.sectionContainer}>
                <Text style={styles.sectionTitle}>
                  {t('common.accommodation_details_screen.cohosts_title')}
                </Text>
                {listing['co-hosts'].length > 0 ? (
                  <View style={styles.coHostsContainer}>
                    {listing['co-hosts'].map((cohost: CoHost) => (
                      <CoHostCard
                        key={cohost.uuid}
                        cohost={cohost}
                        onRemove={handleRemoveCohost}
                        housingUuid={listingUuid}
                      />
                    ))}
                  </View>
                ) : (
                  <Text style={styles.noCoHostsText}>
                    {t('common.accommodation_details_screen.no_cohosts')}
                  </Text>
                )}
              </View>
            )}

            {/* Cohost Invitations List */}
            {listing?.['co-host-invitations'] && (
              <View style={styles.sectionContainer}>
                <Text style={styles.sectionTitle}>
                  {t('common.invitation_card.invitations_title')}
                </Text>
                {listing['co-host-invitations'].length > 0 ? (
                  <View style={styles.invitationsContainer}>
                    {listing['co-host-invitations'].map((invitation: CohostInvitation) => (
                      <CohostInvitationCard
                        key={invitation.uuid}
                        invitation={invitation}
                        onDelete={handleDeleteInvitation}
                      />
                    ))}
                  </View>
                ) : (
                  <Text style={styles.noInvitationsText}>
                    {t('common.invitation_card.no_invitations')}
                  </Text>
                )}
              </View>
            )}

            {/* Cohost Invitation Form */}
            <Animated.View
              ref={inviteSectionRef}
              style={[styles.inviteSection, inviteButtonAnimatedStyle]}
            >
              {showInviteInput && (
                <View style={styles.inputContainer}>
                  <View style={styles.inputWithIconContainer}>
                    <TextInput
                      ref={inputRef}
                      style={[
                        styles.emailInput,
                        showValidation && isEmailValid === false && styles.emailInputError,
                        showValidation && isEmailValid === true && styles.emailInputValid
                      ]}
                      placeholder={t('common.accommodation_details_screen.enter_email')}
                      placeholderTextColor="#999"
                      value={cohostEmail}
                      onChangeText={(text) => {
                        setCohostEmail(text);
                        // Use debounced validation
                        debouncedValidate(text);
                      }}
                      onBlur={() => {
                        // Validate on blur if there's text
                        if (cohostEmail.length > 0) {
                          setIsEmailValid(validateEmail(cohostEmail));
                        }
                      }}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      editable={invitationStatus !== 'loading'}
                      returnKeyType="done"
                      onSubmitEditing={handleInviteCohost}
                      onFocus={handleInputFocus}
                    />
                    {/* Status icon */}
                    {cohostEmail.length > 0 && (
                      <MaterialIcons
                        name={isEmailValid ? "check-circle" : "cancel"}
                        size={20}
                        color={isEmailValid ? "#4CAF50" : (showValidation ? "#f44336" : "#999")}
                        style={styles.inputStatusIcon}
                      />
                    )}
                  </View>
                  {/* Only show validation message when explicitly validating and invalid */}
                  {showValidation && isEmailValid === false && cohostEmail.length > 0 && (
                    <Text style={styles.validationMessage}>
                      {t('common.accommodation_details_screen.invalid_email_message')}
                    </Text>
                  )}
                </View>
              )}
              <TouchableOpacity
                style={[
                  styles.inviteButton,
                  invitationStatus === 'loading' && styles.inviteButtonLoading,
                  invitationStatus === 'success' && styles.inviteButtonSuccess,
                  invitationStatus === 'error' && styles.inviteButtonError,
                  (invitationStatus === 'loading' || (showInviteInput && isEmailValid === false)) && styles.inviteButtonDisabled
                ]}
                onPress={handleInviteCohost}
                disabled={invitationStatus === 'loading' || (showInviteInput && isEmailValid === false)}
              >
                {invitationStatus === 'loading' ? (
                  <ActivityIndicator size="small" color="white" />
                ) : invitationStatus === 'success' ? (
                  <MaterialIcons name="check" size={24} color="white" />
                ) : (
                  <Text style={styles.inviteButtonText}>
                    {showInviteInput
                      ? t('common.accommodation_details_screen.send_invitation')
                      : t('common.accommodation_details_screen.invite_cohost')}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            {/* Delete button - Only visible to the owner */}
            {isOwner && (
              <TouchableOpacity
                style={styles.showDeleteButton}
                onPress={handleDeleteListing}
              >
                <Text style={styles.showDeleteButtonText}>{t('common.accommodation_details_screen.delete_listing')}</Text>
              </TouchableOpacity>
            )}

            {/* Contact Host button - Only visible to cohosts (not the owner) */}
            {!isOwner && (
              <Animated.View style={[styles.contactHostButtonContainer, contactHostButtonAnimatedStyle]}>
                <TouchableOpacity
                  style={styles.contactHostButton}
                  onPress={handleContactHost}
                  disabled={isContactingHost}
                >
                  {isContactingHost ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <MaterialIcons name="chat" size={20} color="#fff" style={styles.contactHostButtonIcon} />
                      <Text style={styles.contactHostButtonText}>
                        {t('common.accommodation_details_screen.contact_host', 'Contact Host')}
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </Animated.View>
            )}

            {/* Leave as Cohost button - Only visible to cohosts (not the owner) */}
            {!isOwner && (
              <Animated.View style={[styles.leaveButtonContainer, leaveButtonAnimatedStyle]}>
                <TouchableOpacity
                  style={styles.leaveButton}
                  onPress={handleLeaveCohost}
                  disabled={isLeavingCohost}
                >
                  {isLeavingCohost ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <MaterialIcons name="exit-to-app" size={20} color="#fff" style={styles.leaveButtonIcon} />
                      <Text style={styles.leaveButtonText}>
                        {t('common.accommodation_details_screen.leave_cohost')}
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </Animated.View>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* PDF Media Modal */}
      {mediaModalVisible && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'white',
          zIndex: 9999,
        }}>
          <SafeAreaView style={{ flex: 1 }}>
            {/* Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#eaeaea',
              backgroundColor: 'white',
            }}>
              {/* Close Button */}
              <TouchableOpacity
                onPress={() => {
                  console.log('Close button pressed');
                  setMediaModalVisible(false);
                  setPdfMedia(null);
                }}
                style={{
                  backgroundColor: '#f0f0f0',
                  borderRadius: 20,
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <View style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <MaterialIcons name="close" size={22} color="#333" />
                </View>
              </TouchableOpacity>

              {/* Title */}
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                flex: 1,
                textAlign: 'center',
                marginHorizontal: 10,
              }}>
                {pdfMedia?.title || pdfMedia?.description || 'Document'}
              </Text>

              {/* Placeholder for symmetry */}
              <View style={{ width: 40 }} />
            </View>

            {/* PDF Content */}
            {pdfMedia && (
              <View style={{ flex: 1 }}>
                <Pdf
                  source={{ uri: pdfMedia.url, cache: true }}
                  style={{ flex: 1 }}
                  trustAllCerts={false}
                  onLoadComplete={(numberOfPages) => console.log(`PDF loaded with ${numberOfPages} pages`)}
                  onError={(error) => {
                    console.error("PDF Error:", error);
                    Alert.alert("Error", "Failed to load PDF. Please try again.");
                  }}
                />
              </View>
            )}
          </SafeAreaView>
        </View>
      )}

      {/* Image Preview */}
      {imagePreviewVisible && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'black',
          zIndex: 9999,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <StatusBar barStyle="light-content" backgroundColor="black" />

          {/* Close button */}
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              backgroundColor: 'rgba(100, 100, 100, 0.8)',
              width: 50,
              height: 50,
              borderRadius: 25,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 10000,
            }}
            onPress={() => {
              console.log('Image close button pressed');
              setImagePreviewVisible(false);
              setSelectedImage(null);
            }}
          >
            <View style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <MaterialIcons name="close" size={28} color="white" />
            </View>
          </TouchableOpacity>

          {/* Image */}
          <Image
            source={{ uri: selectedImage?.url }}
            style={{
              width: Dimensions.get('window').width,
              height: Dimensions.get('window').height * 0.8,
              resizeMode: 'contain',
            }}
          />

          {/* Description */}
          {selectedImage?.description && (
            <Text style={{
              position: 'absolute',
              bottom: 40,
              left: 0,
              right: 0,
              color: 'white',
              fontSize: 16,
              textAlign: 'center',
              paddingHorizontal: 20,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              paddingVertical: 10,
            }}>
              {selectedImage.description}
            </Text>
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: { fontSize: 18, fontWeight: '600', color: '#333', flex: 1, textAlign: 'center' },
  backButton: { width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center' },
  backButtonText: { fontSize: 24, color: '#333' },
  optionsButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginRight: 8
  },
  optionsButtonText: {
    fontSize: 13, // Keeping original size, update if needed
    color: 'white',
    fontWeight: '600'
  },
  carouselContainer: { height: 300, position: 'relative' },
  carouselItemContainer: {
    width: Dimensions.get('window').width,
    height: 300,
    position: 'relative',
  },
  carouselImage: {
    width: '100%',
    height: '100%',
  },
  imageCountIndicator: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  imageCountText: { color: '#fff', fontSize: 14, fontWeight: '600' },
  amenityChip: {
    position: 'absolute',
    left: 16,
    bottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    maxWidth: '60%',
  },
  amenityChipContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  amenityChipText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    flexShrink: 1,
  },
  dotContainer: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: { width: 8, height: 8, borderRadius: 4, marginHorizontal: 4 },
  detailsContainer: { padding: 16 },
  title: { fontSize: 20, fontWeight: 'bold', color: '#333', marginBottom: 16 },
  sectionContainer: { marginTop: 24 },
  sectionHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    flex: 1,
  },
  sectionTitleDecoration: {
    height: 3,
    width: 40,
    backgroundColor: '#3498DB',
    borderRadius: 2,
    marginTop: 8,
  },
  description: { fontSize: 16, color: '#666', lineHeight: 24 },

  // Media section styles
  mediaGalleryContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f9f9f9',
    padding: 12,
  },
  mediaListContent: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  mediaContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  mediaItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 120,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  mediaIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  mediaIconWrapper: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  mediaIcon: { fontSize: 24 },
  mediaText: {
    fontSize: 13,
    color: '#333',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },


  // Amenities section styles
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 12,
  },
  amenityItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 20,
    padding: 8,
    borderRadius: 12,
  },
  amenityIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e6f2ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedAmenityIconContainer: {
    backgroundColor: '#3498DB',
    transform: [{ scale: 1.1 }],
  },
  amenityText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedAmenityText: {
    color: '#3498DB',
    fontWeight: 'bold',
  },
  selectedAmenity: {
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
    borderRadius: 12,
  },
  showDeleteButton: { backgroundColor: '#dd9393', paddingVertical: 12, borderRadius: 8, alignItems: 'center' },
  showDeleteButtonText: { fontSize: 16, color: '#ffffff', fontWeight: '500' },
  // Inline Player Styles
  inlinePlayerWrapper: { marginVertical: 8 },
  inlinePlayerContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  inlinePlayerHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', margin: 8 },
  inlinePlayerTitle: { fontSize: 16, fontWeight: '600', color: '#333' },
  inlinePlayerClose: { fontSize: 32, color: '#333' },
  inlineVideoPlayer: { width: '100%', height: 200, borderRadius: 16 },
  inlineAudioPlayer: { width: '100%', height: 80, borderRadius: 16 },
  // Modal Styles for PDF
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
    zIndex: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'center'
  },
  closeButton: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  closeButtonText: {
    fontSize: 32,
    color: '#333',
    fontWeight: 'bold',
  },
  placeholder: { width: 50 },
  poiListContainer: {
    marginTop: 15,
    marginBottom: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 10,
  },
  poiListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  poiItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginBottom: 8,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#4A90E2',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  poiItemContent: {
    flex: 1,
    padding: 12,
  },
  poiName: {
    fontSize: 15,
    fontWeight: '600',
  },
  poiAddress: {
    fontSize: 13,
    color: '#666',
    marginTop: 3,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  // This style is now defined in the enhanced styles section above
  mediaThumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  imagePreviewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  imagePreviewOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  imagePreviewCloseButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    right: 20,
    zIndex: 10000,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(104, 104, 104, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 8,
  },
  imagePreviewCloseText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  imagePreview: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.8,
    backgroundColor: 'transparent',
  },
  imagePreviewDescription: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 20,
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingVertical: 10,
    zIndex: 5,
  },
  inviteSection: {
    marginBottom: 16,
    marginTop: 16,
  },
  invitationsContainer: {
    marginTop: 8,
  },
  noInvitationsText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
    marginBottom: 8,
  },
  emailInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#ddd',
    flex: 1, // Take up available space
  },
  emailInputError: {
    borderColor: '#f44336',
    backgroundColor: '#ffebee',
  },
  emailInputValid: {
    borderColor: '#4CAF50',
    backgroundColor: '#f1f8e9',
  },
  inputContainer: {
    marginBottom: 12,
  },
  inputWithIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    position: 'relative',
  },
  inputStatusIcon: {
    position: 'absolute',
    right: 12,
  },
  validationMessage: {
    color: '#f44336',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  inviteButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  inviteButtonDisabled: {
    backgroundColor: '#cccccc',
    opacity: 0.7,
  },
  inviteButtonLoading: {
    backgroundColor: '#4A90E2',
  },
  inviteButtonSuccess: {
    backgroundColor: '#4CAF50',
  },
  inviteButtonError: {
    backgroundColor: '#f44336',
  },
  inviteButtonText: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '500',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  pagination: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#fff',
  },
  headerButton: {
    minWidth: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerStyle: {
    backgroundColor: '#fff',
    elevation: 0, // Remove shadow on Android
    shadowOpacity: 0, // Remove shadow on iOS
  },
  headerTitleStyle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  // Styles for cohosts list
  coHostsContainer: {
    marginTop: 10,
  },
  noCoHostsText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 10,
    marginBottom: 10,
  },
  // Styles for cohost card
  cohostCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cohostInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cohostAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cohostInitial: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  cohostName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  cohostActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4A90E2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    minHeight: 36,
  },
  contactButtonLoading: {
    opacity: 0.7,
  },
  contactButtonIcon: {
    marginRight: 6,
  },
  contactButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  removeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF4B4B',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    minHeight: 36,
  },
  removeButtonLoading: {
    opacity: 0.7,
  },
  removeButtonSuccess: {
    backgroundColor: '#4CAF50',
  },
  removeButtonIcon: {
    marginRight: 6,
  },
  removeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  // Styles for the Contact Host button
  contactHostButtonContainer: {
    marginTop: 20,
    width: '100%',
  },
  contactHostButton: {
    backgroundColor: '#4A90E2', // Blue color for the contact button
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactHostButtonIcon: {
    marginRight: 8,
  },
  contactHostButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Styles for the Leave as Cohost button
  leaveButtonContainer: {
    marginTop: 12,
    marginBottom: 20,
    width: '100%',
  },
  leaveButton: {
    backgroundColor: '#FF5722', // Orange color for the leave button
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  leaveButtonIcon: {
    marginRight: 8,
  },
  leaveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AccommodationDetailsScreen;
