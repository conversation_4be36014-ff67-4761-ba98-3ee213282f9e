import { View, Text, Image, StyleSheet, TouchableOpacity, Linking, ScrollView, Platform, Alert, ActivityIndicator } from 'react-native';
import React, { useEffect, useRef, useCallback } from 'react';
import { Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { httpClient } from '@/utils/http';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type BookingDetailsScreenProps = {
  route: RouteProp<{ params: { bookingUuid: string } }, 'params'>;
  navigation: NativeStackNavigationProp<any>;
};

interface BookingClient {
  displayName: string;
  email: string;
  phoneNumber: string;
}

interface BookingHousing {
  uuid: string;
  title: string;
  description: string;
  address: string;
  coverImage?: string;
}

interface BookingData {
  uuid: string;
  arrivalDate: string;
  departureDate: string;
  adultCount: number;
  childCount: number;
  reservationCode: string;
  client: BookingClient;
  housing: BookingHousing;
}

const BookingDetailsScreen = ({ route, navigation }: BookingDetailsScreenProps) => {
  const { t } = useTranslation();
  const { bookingUuid } = route.params;
  
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(100)).current;

  // Fetch booking details using React Query
  const { 
    data: booking, 
    isLoading, 
    isError, 
    refetch 
  } = useQuery({
    queryKey: ['housing', bookingUuid],
    queryFn: async () => {
      const response = await httpClient.get<{ data: BookingData }>(`/housings/reservations/getone/${bookingUuid}`);
      console.log("housing: ", response.data);
      return response.data;
    }
  });

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log("Screen focused, refreshing booking details");
      refetch();
    }, [refetch])
  );

  useEffect(() => {
    if (booking) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [booking, fadeAnim, slideAnim]);

  const handlePhoneCall = (phoneNumber: string) => {
    let phoneUrl = `tel:${phoneNumber}`;
    Linking.canOpenURL(phoneUrl)
      .then(supported => {
        console.log('Supported:', supported);
        if (supported) {
          return Linking.openURL(phoneUrl);
        }
      })
      .catch(error => console.log('Error making phone call:', error));
  };

  const handleEmail = (email: string) => {
    let emailUrl = `mailto:${email}`;
    Linking.canOpenURL(emailUrl)
      .then(supported => {
        if (supported) {
          return Linking.openURL(emailUrl);
        }
      })
      .catch(error => console.log('Error opening email client:', error));
  };

  // Format dates
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleDeleteBooking = (uuid: string) => {
    Alert.alert(
      t('details.deleteConfirmTitle'),
      t('details.deleteConfirmMessage'),
      [
        {
          text: t('details.cancel'),
          style: "cancel"
        },
        {
          text: t('details.delete'),
          style: "destructive",
          onPress: async () => {
            try {
              await httpClient.delete(`/housings/reservations/delete/${uuid}`);
              Alert.alert(t('details.deleteSuccess'));
              navigation.goBack();
            } catch (error) {
              console.error("Error deleting booking:", error);
              Alert.alert(t('details.deleteError'));
            }
          }
        }
      ]
    );
  };

  // Loading and error states
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('details.loadingError')}</Text>
        <TouchableOpacity 
          onPress={() => {
            // Use an inline function to handle the event
            refetch();
          }} 
          style={styles.retryButton}
        >
          <Text>{t('details.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Ensure booking exists before rendering
  if (!booking) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('details.noBooking')}</Text>
      </View>
    );
  }

  // Calculate stay duration
  const arrivalDate = new Date(booking.arrivalDate);
  const departureDate = new Date(booking.departureDate);
  const daysDifference = Math.ceil((departureDate.getTime() - arrivalDate.getTime()) / 86400000);
  const stayDurationText = daysDifference <= 1 
    ? t('details.day_one')
    : t('details.day_other', { count: daysDifference });

  return (
    <ScrollView style={styles.container}>
      {/* <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity> */}
      
      <Animated.View 
        style={[
          styles.imageContainer, 
          { opacity: fadeAnim }
        ]}
      >
        <Image 
          source={{ uri: booking.housing.coverImage || 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80' }} 
          style={styles.image} 
          resizeMode="cover"
        />
      </Animated.View>

      <Animated.View 
        style={[
          styles.detailsContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <Text style={styles.title}>{t('details.title')}</Text>
        
        <View style={styles.dateContainer}>
          <View style={styles.calendarIcon}>
            <Ionicons name="calendar-outline" size={24} color="#333" />
          </View>
          <Text style={styles.dateText}>
            {t('details.from')} {formatDate(booking.arrivalDate)} {t('details.to')} {formatDate(booking.departureDate)}, {stayDurationText}
          </Text>
        </View>
        
        <View style={styles.guestsContainer}>
          <View style={styles.guestsIcon}>
            <Ionicons name="people-outline" size={24} color="#333" />
          </View>
          <Text style={styles.guestsText}>
            {booking.adultCount === 1 
              ? t('details.adult_one', { count: booking.adultCount }) 
              : t('details.adult_other', { count: booking.adultCount })} • 
            {booking.childCount === 1 
              ? t('details.child_one', { count: booking.childCount }) 
              : t('details.child_other', { count: booking.childCount })}
          </Text>
        </View>
        
        <View style={styles.accommodationInfoContainer}>
          <Text style={styles.accommodationLabel}>{booking.housing.title}</Text>
          <Text style={styles.accommodationValue}>{booking.housing.address}</Text>
        </View>
        
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>{t('details.code')}: </Text>
          <Text style={styles.priceValue}>{booking.reservationCode}</Text>
        </View>

        <View style={styles.divider} />

        <Text style={styles.sectionTitle}>{t('details.contactInfo')}</Text>

        <View style={styles.contactCard}>
          <View style={styles.infoItem}>
            <Ionicons name="mail-outline" size={20} color="#555" style={styles.contactIcon} />
            <Text style={styles.infoLabel}>{t('details.email')}:</Text>
            <TouchableOpacity onPress={() => handleEmail(booking.client.email)}>
              <Text style={styles.infoValueLink}>{booking.client.email}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="call-outline" size={20} color="#555" style={styles.contactIcon} />
            <Text style={styles.infoLabel}>{t('details.phone')}:</Text>
            <TouchableOpacity onPress={() => handlePhoneCall(booking.client.phoneNumber)}>
              <Text style={styles.infoValueLink}>{booking.client.phoneNumber}</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>{t('details.guestInfo')}</Text>
        
        <View style={styles.guestInfoCard}>
          <View style={styles.infoItem}>
            <Ionicons name="person-outline" size={20} color="#555" style={styles.contactIcon} />
            <Text style={styles.infoLabel}>{t('details.guest')}:</Text>
            <Text style={styles.infoValue}>{booking.client.displayName}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>{t('details.housingDetails')}</Text>
        
        <View style={styles.housingInfoCard}>
          <Text style={styles.housingId}>{t('details.id')}: {booking.housing.uuid.substring(0, 8)}...</Text>
          <Text style={styles.housingDescription}>
            {booking.housing.title}
          </Text>
          <Text style={styles.housingDescription}>
            {booking.housing.description}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.adjustButton}
          onPress={() => navigation.navigate('BookingUpdate', { booking: booking })}
        >
          <Text style={styles.adjustButtonText}>{t('details.update')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.adjustButton}
          onPress={() => handleDeleteBooking(booking.uuid)}
        >
          <Text style={styles.adjustButtonText}>{t('details.delete')}</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    padding: 8,
  },
  imageContainer: {
    width: '100%',
    height: 180, // Reduced height to give more space to reservation info
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  detailsContainer: {
    padding: 20,
    backgroundColor: 'white',
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  calendarIcon: {
    marginRight: 10,
  },
  dateText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  guestsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  guestsIcon: {
    marginRight: 10,
  },
  guestsText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  accommodationInfoContainer: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  accommodationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 4,
  },
  accommodationValue: {
    fontSize: 14,
    color: '#333',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  contactCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  guestInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  housingInfoCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  housingId: {
    fontSize: 12,
    color: '#777',
    marginBottom: 5,
  },
  housingDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  contactIcon: {
    marginRight: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    width: 80,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  infoValueLink: {
    fontSize: 14,
    color: '#0066cc',
    textDecorationLine: 'underline',
    flex: 1,
  },
  priceContainer: {
    flexDirection: 'row',
    backgroundColor: '#edf7ed',
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
    marginBottom: 5,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#c8e6c9',
  },
  priceLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2e7d32',
  },
  priceValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2e7d32',
    marginLeft: 'auto',
  },
  adjustButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  adjustButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
  },
});

export default BookingDetailsScreen;