import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useMutation } from '@tanstack/react-query';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { httpClient } from '@/utils/http';
import { useNavigation } from '@react-navigation/native';
import HousingSelect from '@/components/shared/HousingSelect';
import { useTranslation } from 'react-i18next';

// Types
interface ClientInfo {
  phoneNumber: string;
  email: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
}

interface BookingPayload {
  housingUuid: string;
  arrivalDate: string;
  departureDate: string;
  adultCount: number;
  childCount: number;
  price: number;
  currency: string;
  client: ClientInfo;
}

const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const BookingUpdateScreen = ({ route }: any) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { booking } = route.params;
  console.log('booking', booking);

  // State for form fields
  const [arrivalDate, setArrivalDate] = useState(new Date(booking?.arrivalDate));
  const [departureDate, setDepartureDate] = useState(booking?.arrivalDate ? new Date(booking?.departureDate) : new Date(new Date().setDate(new Date().getDate() + 1)));
  const [adultCount, setAdultCount] = useState(booking?.adultCount || 1);
  const [childCount, setChildCount] = useState(booking?.childCount || 0);
  const [phoneNumber, setPhoneNumber] = useState(booking?.client?.phoneNumber || '');
  const [email, setEmail] = useState(booking?.client?.email || '');
  const [firstName, setFirstName] = useState(booking?.client?.displayName.split(' ')[0] || ''); // "displayName":"Firstname Lastname",
  const [lastName, setLastName] = useState(booking?.client?.displayName.split(' ')[1] || '');
  const [dateOfBirth, setDateOfBirth] = useState(new Date(1990, 0, 1));
  const [gender, setGender] = useState<'MALE' | 'FEMALE'>(booking?.client?.gender || 'MALE');
  const [housingUuid, setHousingUuid] = useState(booking?.housing?.uuid);

  // State for date pickers
  const [showArrivalDatePicker, setShowArrivalDatePicker] = useState(false);
  const [showDepartureDatePicker, setShowDepartureDatePicker] = useState(false);
  const [showDateOfBirthPicker, setShowDateOfBirthPicker] = useState(false);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Calculate total nights and total price
  const nightsCount = Math.max(1, Math.ceil((departureDate.getTime() - arrivalDate.getTime()) / (1000 * 60 * 60 * 24)));
  //const totalPrice = price * nightsCount;

  // Create booking mutation with @tanstack/react-query syntax
  const createBookingMutation = useMutation({
    mutationFn: (bookingData: BookingPayload) => 
      httpClient.put<{ success: boolean; id?: string }>(`/housings/reservations/update/${booking?.uuid}`, bookingData),
    onSuccess: (data) => {
      Alert.alert(
        t('update.bookingSuccess'),
        t('update.bookingSuccessMessage'),
        [
          {
            text: t('creation.ok'),
            onPress: () => navigation.goBack(),
          },
        ]
      );
    },
    onError: (error: any) => {
      Alert.alert(
        t('update.bookingFailed'),
        error.response?.data?.message || t('update.genericError'),
      );
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!phoneNumber && !email) {
      Alert.alert(t('creation.validationError'), t('creation.contactRequired'));
      return false;
    } else {
      if (phoneNumber && !/^\+[0-9]{10,15}$/.test(phoneNumber)) {
        newErrors.phoneNumber = t('creation.invalidPhoneFormat');
      }
      if (email && !/\S+@\S+\.\S+/.test(email)) {
        newErrors.email = t('creation.invalidEmailFormat');
      }
    }

    if (!firstName) newErrors.firstName = t('creation.firstNameRequired');
    if (!lastName) newErrors.lastName = t('creation.lastNameRequired');

    if (arrivalDate >= departureDate) newErrors.dates = t('creation.departureDateError');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    const bookingData: BookingPayload = {
      housingUuid,
      arrivalDate: formatDate(arrivalDate),
      departureDate: formatDate(departureDate),
      adultCount,
      childCount,
      price: booking?.price || 0,
      currency: booking?.currency || 'USD',
      client: {
        phoneNumber,
        email,
        firstName,
        lastName,
        dateOfBirth: formatDate(dateOfBirth),
        gender,
      },
    };

    createBookingMutation.mutate(bookingData);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* <Text style={styles.title}>{t('creation.title')}</Text> */}

        {/* Accommodation Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('creation.accommodationSelectionTitle')}</Text>
          <HousingSelect
            onSelect={(uuid: any) => {
              console.log(uuid);
              setHousingUuid(uuid);
            }}
            initialValue={booking?.housing?.uuid}
          />
        </View>

        {/* Date Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('creation.stayDetailsTitle')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.arrivalDate')}</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowArrivalDatePicker(true)}
            >
              <Text>{formatDate(arrivalDate)}</Text>
            </TouchableOpacity>
            {showArrivalDatePicker && (
              <DateTimePicker
                value={arrivalDate}
                mode="date"
                display="default"
                minimumDate={new Date()}
                onChange={(event, selectedDate) => {
                  setShowArrivalDatePicker(false);
                  if (selectedDate) {
                    setArrivalDate(selectedDate);
                    // Update departure date if it's before new arrival date
                    if (selectedDate >= departureDate) {
                      const newDepartureDate = new Date(selectedDate);
                      newDepartureDate.setDate(newDepartureDate.getDate() + 1);
                      setDepartureDate(newDepartureDate);
                    }
                  }
                }}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.departureDate')}</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDepartureDatePicker(true)}
            >
              <Text>{formatDate(departureDate)}</Text>
            </TouchableOpacity>
            {showDepartureDatePicker && (
              <DateTimePicker
                value={departureDate}
                mode="date"
                display="default"
                minimumDate={new Date(arrivalDate.getTime() + 86400000)} // +1 day
                onChange={(event, selectedDate) => {
                  setShowDepartureDatePicker(false);
                  if (selectedDate) {
                    setDepartureDate(selectedDate);
                  }
                }}
              />
            )}
          </View>
          
          {errors.dates && <Text style={styles.errorText}>{errors.dates}</Text>}

          <View style={styles.row}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={styles.label}>{t('creation.adults')}</Text>
              <View style={styles.counterContainer}>
                <TouchableOpacity
                  style={styles.counterButton}
                  onPress={() => setAdultCount(Math.max(1, adultCount - 1))}
                >
                  <Text style={styles.counterButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={styles.counterText}>{adultCount}</Text>
                <TouchableOpacity
                  style={styles.counterButton}
                  onPress={() => setAdultCount(adultCount + 1)}
                >
                  <Text style={styles.counterButtonText}>+</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={styles.label}>{t('creation.children')}</Text>
              <View style={styles.counterContainer}>
                <TouchableOpacity
                  style={styles.counterButton}
                  onPress={() => setChildCount(Math.max(0, childCount - 1))}
                >
                  <Text style={styles.counterButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={styles.counterText}>{childCount}</Text>
                <TouchableOpacity
                  style={styles.counterButton}
                  onPress={() => setChildCount(childCount + 1)}
                >
                  <Text style={styles.counterButtonText}>+</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Client Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('creation.personalInfoTitle')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.firstName')}</Text>
            <TextInput
              style={styles.input}
              value={firstName}
              onChangeText={setFirstName}
              placeholder={t('creation.firstNamePlaceholder')}
            />
            {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.lastName')}</Text>
            <TextInput
              style={styles.input}
              value={lastName}
              onChangeText={setLastName}
              placeholder={t('creation.lastNamePlaceholder')}
            />
            {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.phoneNumber')}</Text>
            <TextInput
              style={styles.input}
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder={t('creation.phonePlaceholder')}
              keyboardType="phone-pad"
            />
            {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.email')}</Text>
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder={t('creation.emailPlaceholder')}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.dateOfBirth')}</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDateOfBirthPicker(true)}
            >
              <Text>{formatDate(dateOfBirth)}</Text>
            </TouchableOpacity>
            {showDateOfBirthPicker && (
              <DateTimePicker
                value={dateOfBirth}
                mode="date"
                display="default"
                maximumDate={new Date()}
                onChange={(event, selectedDate) => {
                  setShowDateOfBirthPicker(false);
                  if (selectedDate) {
                    setDateOfBirth(selectedDate);
                  }
                }}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>{t('creation.gender')}</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={gender}
                onValueChange={(itemValue) => setGender(itemValue as 'MALE' | 'FEMALE')}
                style={styles.picker}
              >
                <Picker.Item label={t('creation.male')} value="MALE" />
                <Picker.Item label={t('creation.female')} value="FEMALE" />
              </Picker>
            </View>
          </View>
        </View>

        {/* Price Summary */}
        {/* <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Summary</Text>
          <View style={styles.priceSummary}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Price per night</Text>
              <Text style={styles.priceValue}>{price} {currency}</Text>
            </View>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Nights</Text>
              <Text style={styles.priceValue}>{nightsCount}</Text>
            </View>
            <View style={[styles.priceRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>{totalPrice} {currency}</Text>
            </View>
          </View>
        </View> */}

        {/* Submit Button */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={createBookingMutation.isPending}
        >
          {createBookingMutation.isPending ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.submitButtonText}>{t('update.updateBooking')}</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#3498DB',
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#D1987C',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    height: 46,
  },
  counterButton: {
    width: 46,
    height: 46,
    justifyContent: 'center',
    alignItems: 'center',
  },
  counterButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#3498DB',
  },
  counterText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
  },
  priceSummary: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderColor: '#E0E0E0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498DB',
  },
  submitButton: {
    backgroundColor: '#3498DB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BookingUpdateScreen;