import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  ReactNode,
  useCallback,
} from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';
import { appUpdateService, UpdateInfo, UpdateOptions } from '@/services/AppUpdateService';
import { IAUUpdateKind } from 'sp-react-native-in-app-updates';

interface AppUpdateContextType {
  updateInfo: UpdateInfo | null;
  isChecking: boolean;
  error: string | null;
  checkForUpdate: () => Promise<void>;
  startUpdate: (options?: UpdateOptions) => Promise<void>;
  dismissUpdate: () => void;
  // Android-specific
  installUpdate: () => Promise<void>;
  downloadProgress: number;
  installStatus: string | null;
}

const AppUpdateContext = createContext<AppUpdateContextType | undefined>(undefined);

interface AppUpdateProviderProps {
  children: ReactNode;
  checkOnLaunch?: boolean;
  checkOnResume?: boolean;
  checkInterval?: number; // in milliseconds
}

export const AppUpdateProvider: React.FC<AppUpdateProviderProps> = ({
  children,
  checkOnLaunch = true,
  checkOnResume = true,
  checkInterval = 24 * 60 * 60 * 1000, // 24 hours
}) => {
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [installStatus, setInstallStatus] = useState<string | null>(null);

  const lastCheckRef = useRef<number>(0);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Check for updates
  const checkForUpdate = useCallback(async () => {
    const now = Date.now();
    
    // Avoid checking too frequently
    if (now - lastCheckRef.current < checkInterval) {
      return;
    }

    setIsChecking(true);
    setError(null);

    try {
      const info = await appUpdateService.checkForUpdate();
      setUpdateInfo(info);
      lastCheckRef.current = now;
      
      if (info.hasUpdate) {
        console.log('AppUpdateContext: Update available:', info);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check for updates';
      setError(errorMessage);
      console.error('AppUpdateContext: Error checking for updates:', err);
    } finally {
      setIsChecking(false);
    }
  }, [checkInterval]);

  // Start update process
  const startUpdate = useCallback(async (options: UpdateOptions = {}) => {
    if (!updateInfo?.hasUpdate) {
      console.warn('AppUpdateContext: No update available to start');
      return;
    }

    try {
      setError(null);
      
      // Set default options based on platform
      const defaultOptions: UpdateOptions = Platform.select({
        android: {
          updateType: IAUUpdateKind.FLEXIBLE,
          ...options,
        },
        ios: {
          title: 'Update Available',
          message: 'A new version of Wodaabe Stays is available. Would you like to update now?',
          buttonUpgradeText: 'Update',
          buttonCancelText: 'Later',
          forceUpgrade: false,
          ...options,
        },
      }) as UpdateOptions;

      await appUpdateService.startUpdate(defaultOptions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start update';
      setError(errorMessage);
      console.error('AppUpdateContext: Error starting update:', err);
    }
  }, [updateInfo]);

  // Install update (Android only)
  const installUpdate = useCallback(async () => {
    if (Platform.OS !== 'android') {
      console.warn('AppUpdateContext: installUpdate is only available on Android');
      return;
    }

    try {
      setError(null);
      await appUpdateService.installUpdate();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to install update';
      setError(errorMessage);
      console.error('AppUpdateContext: Error installing update:', err);
    }
  }, []);

  // Dismiss update notification
  const dismissUpdate = useCallback(() => {
    setUpdateInfo(null);
    setError(null);
  }, []);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        checkOnResume &&
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        // App has come to the foreground
        checkForUpdate();
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [checkForUpdate, checkOnResume]);

  // Android-specific status listeners
  useEffect(() => {
    if (Platform.OS === 'android') {
      const statusCallback = (status: any) => {
        console.log('AppUpdateContext: Android update status:', status);
        
        if (status.bytesDownloaded && status.totalBytesToDownload) {
          const progress = (status.bytesDownloaded / status.totalBytesToDownload) * 100;
          setDownloadProgress(progress);
        }
        
        // Map status codes to readable strings
        const statusMap: { [key: number]: string } = {
          0: 'Unknown',
          1: 'Pending',
          2: 'Downloading',
          3: 'Installing',
          4: 'Installed',
          5: 'Failed',
          6: 'Canceled',
          11: 'Downloaded',
        };
        
        setInstallStatus(statusMap[status.status] || 'Unknown');
      };

      appUpdateService.addStatusUpdateListener(statusCallback);
      
      return () => {
        appUpdateService.removeStatusUpdateListener(statusCallback);
      };
    }
  }, []);

  // Initial check on mount
  useEffect(() => {
    if (checkOnLaunch) {
      checkForUpdate();
    }
  }, [checkForUpdate, checkOnLaunch]);

  const value: AppUpdateContextType = {
    updateInfo,
    isChecking,
    error,
    checkForUpdate,
    startUpdate,
    dismissUpdate,
    installUpdate,
    downloadProgress,
    installStatus,
  };

  return (
    <AppUpdateContext.Provider value={value}>
      {children}
    </AppUpdateContext.Provider>
  );
};

export const useAppUpdate = (): AppUpdateContextType => {
  const context = useContext(AppUpdateContext);
  if (context === undefined) {
    throw new Error('useAppUpdate must be used within an AppUpdateProvider');
  }
  return context;
};
