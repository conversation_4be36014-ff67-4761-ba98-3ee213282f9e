import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppUpdate } from '@/hooks/useAppUpdate';
import Constants from 'expo-constants';

interface UpdateSettingsSectionProps {
  style?: any;
}

export const UpdateSettingsSection: React.FC<UpdateSettingsSectionProps> = ({ style }) => {
  const { t } = useTranslation();
  const {
    updateInfo,
    checkForUpdate,
    startFlexibleUpdate,
    isChecking,
    error,
    getVersionInfo,
  } = useAppUpdate();

  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const handleCheckForUpdate = async () => {
    try {
      const info = await checkForUpdate(true); // Force check
      setLastChecked(new Date());
      
      if (info?.hasUpdate) {
        Alert.alert(
          t('settings.update.available_title', 'Update Available'),
          t('settings.update.available_message', 'A new version ({{version}}) is available. Would you like to update now?', {
            version: info.storeVersion
          }),
          [
            {
              text: t('settings.update.later', 'Later'),
              style: 'cancel',
            },
            {
              text: t('settings.update.update_now', 'Update Now'),
              onPress: () => startFlexibleUpdate(),
            },
          ]
        );
      } else {
        Alert.alert(
          t('settings.update.up_to_date_title', 'Up to Date'),
          t('settings.update.up_to_date_message', 'You are using the latest version of the app.'),
          [{ text: t('common.ok', 'OK') }]
        );
      }
    } catch (err) {
      Alert.alert(
        t('settings.update.error_title', 'Update Check Failed'),
        t('settings.update.error_message', 'Unable to check for updates. Please try again later.'),
        [{ text: t('common.ok', 'OK') }]
      );
    }
  };

  const versionInfo = getVersionInfo();
  const currentVersion = versionInfo.currentVersion || Constants.expoConfig?.version || '1.0.0';

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.sectionTitle}>
        {t('settings.update.section_title', 'App Updates')}
      </Text>

      {/* Current Version */}
      <View style={styles.settingRow}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingLabel}>
            {t('settings.update.current_version', 'Current Version')}
          </Text>
          <Text style={styles.settingValue}>{currentVersion}</Text>
        </View>
      </View>

      {/* Update Status */}
      {updateInfo && (
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>
              {t('settings.update.status', 'Update Status')}
            </Text>
            <Text style={[
              styles.settingValue,
              updateInfo.hasUpdate ? styles.updateAvailable : styles.upToDate
            ]}>
              {updateInfo.hasUpdate 
                ? t('settings.update.available', 'Update Available ({{version}})', {
                    version: updateInfo.storeVersion
                  })
                : t('settings.update.up_to_date', 'Up to Date')
              }
            </Text>
          </View>
        </View>
      )}

      {/* Last Checked */}
      {lastChecked && (
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>
              {t('settings.update.last_checked', 'Last Checked')}
            </Text>
            <Text style={styles.settingValue}>
              {lastChecked.toLocaleString()}
            </Text>
          </View>
        </View>
      )}

      {/* Check for Updates Button */}
      <TouchableOpacity
        style={[styles.checkButton, isChecking && styles.checkButtonDisabled]}
        onPress={handleCheckForUpdate}
        disabled={isChecking}
      >
        {isChecking ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.checkButtonText}>
            {t('settings.update.check_for_updates', 'Check for Updates')}
          </Text>
        )}
      </TouchableOpacity>

      {/* Update Available Button */}
      {updateInfo?.hasUpdate && (
        <TouchableOpacity
          style={styles.updateButton}
          onPress={() => startFlexibleUpdate()}
        >
          <Text style={styles.updateButtonText}>
            {t('settings.update.update_now', 'Update Now')}
          </Text>
        </TouchableOpacity>
      )}

      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t('settings.update.error', 'Error: {{error}}', { error })}
          </Text>
        </View>
      )}

      {/* Platform Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          {Platform.OS === 'android' 
            ? t('settings.update.android_info', 'Updates on Android are downloaded and installed automatically.')
            : t('settings.update.ios_info', 'Updates on iOS will redirect you to the App Store.')
          }
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  settingInfo: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 2,
  },
  settingValue: {
    fontSize: 14,
    color: '#6B7280',
  },
  updateAvailable: {
    color: '#059669',
    fontWeight: '500',
  },
  upToDate: {
    color: '#6B7280',
  },
  checkButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  checkButtonDisabled: {
    opacity: 0.6,
  },
  checkButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  updateButton: {
    backgroundColor: '#059669',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  updateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
  },
  infoContainer: {
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  infoText: {
    color: '#6B7280',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});
