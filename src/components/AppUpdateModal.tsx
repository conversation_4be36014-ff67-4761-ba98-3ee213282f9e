import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppUpdate } from '@/hooks/useAppUpdate';
import { IAUUpdateKind } from 'sp-react-native-in-app-updates';

interface AppUpdateModalProps {
  visible: boolean;
  onClose?: () => void;
  forceUpdate?: boolean;
  customTitle?: string;
  customMessage?: string;
}

export const AppUpdateModal: React.FC<AppUpdateModalProps> = ({
  visible,
  onClose,
  forceUpdate = false,
  customTitle,
  customMessage,
}) => {
  const { t } = useTranslation();
  const {
    updateInfo,
    startUpdate,
    dismissUpdate,
    downloadProgress,
    installStatus,
    isChecking,
  } = useAppUpdate();

  const handleUpdate = async () => {
    try {
      await startUpdate({
        title: customTitle || t('update.modal.title', 'Update Available'),
        message: customMessage || t('update.modal.message', 'A new version is available. Would you like to update now?'),
        buttonUpgradeText: t('update.modal.update_button', 'Update'),
        buttonCancelText: t('update.modal.later_button', 'Later'),
        forceUpgrade: forceUpdate,
        updateType: forceUpdate ? IAUUpdateKind.IMMEDIATE : IAUUpdateKind.FLEXIBLE,
      });
    } catch (error) {
      console.error('Error starting update:', error);
    }
  };

  const handleClose = () => {
    if (!forceUpdate) {
      dismissUpdate();
      onClose?.();
    }
  };

  if (!visible || !updateInfo?.hasUpdate) {
    return null;
  }

  const isAndroidDownloading = Platform.OS === 'android' && 
    (installStatus === 'Downloading' || downloadProgress > 0);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          {/* App Icon or Update Icon */}
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>📱</Text>
          </View>

          {/* Title */}
          <Text style={styles.titleText}>
            {customTitle || t('update.modal.title', 'Update Available')}
          </Text>

          {/* Version Info */}
          {updateInfo.storeVersion && (
            <Text style={styles.versionText}>
              {t('update.modal.version_info', 'Version {{version}} is now available', {
                version: updateInfo.storeVersion
              })}
            </Text>
          )}

          {/* Message */}
          <Text style={styles.messageText}>
            {customMessage || t('update.modal.message', 
              'This update includes bug fixes and improvements to enhance your experience.'
            )}
          </Text>

          {/* Android Download Progress */}
          {isAndroidDownloading && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                {installStatus} {downloadProgress > 0 && `(${Math.round(downloadProgress)}%)`}
              </Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${downloadProgress}%` }
                  ]} 
                />
              </View>
            </View>
          )}

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.updateButton]}
              onPress={handleUpdate}
              disabled={isChecking || isAndroidDownloading}
            >
              {isChecking ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.updateButtonText}>
                  {t('update.modal.update_button', 'Update Now')}
                </Text>
              )}
            </TouchableOpacity>

            {!forceUpdate && (
              <TouchableOpacity
                style={[styles.button, styles.laterButton]}
                onPress={handleClose}
                disabled={isChecking || isAndroidDownloading}
              >
                <Text style={styles.laterButtonText}>
                  {t('update.modal.later_button', 'Later')}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Force update notice */}
          {forceUpdate && (
            <Text style={styles.forceUpdateText}>
              {t('update.modal.force_update_notice', 
                'This update is required to continue using the app.'
              )}
            </Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxWidth: 340,
    width: '90%',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconText: {
    fontSize: 30,
  },
  titleText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1F2937',
  },
  versionText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 12,
  },
  messageText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#374151',
    lineHeight: 22,
  },
  progressContainer: {
    width: '100%',
    marginBottom: 20,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  buttonContainer: {
    flexDirection: 'column',
    width: '100%',
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  updateButton: {
    backgroundColor: '#3B82F6',
  },
  updateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  laterButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  laterButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '500',
  },
  forceUpdateText: {
    fontSize: 12,
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
});
