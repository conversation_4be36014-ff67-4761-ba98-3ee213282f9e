import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useAppUpdate } from '@/hooks/useAppUpdate';
import { AppUpdateModal } from '@/components/AppUpdateModal';

/**
 * Example component demonstrating how to use the app update system
 * This can be used in a settings screen or as a standalone component
 */
export const UpdateExample: React.FC = () => {
  const {
    updateInfo,
    checkForUpdate,
    startFlexibleUpdate,
    startImmediateUpdate,
    startUpdateWithMessage,
    hasUpdate,
    isChecking,
    getVersionInfo,
    error,
  } = useAppUpdate();

  const [showModal, setShowModal] = useState(false);

  const handleCheckUpdate = async () => {
    await checkForUpdate(true); // Force check
  };

  const handleFlexibleUpdate = async () => {
    if (hasUpdate()) {
      await startFlexibleUpdate();
    } else {
      Alert.alert('No Update', 'No update available');
    }
  };

  const handleImmediateUpdate = async () => {
    if (hasUpdate()) {
      await startImmediateUpdate();
    } else {
      Alert.alert('No Update', 'No update available');
    }
  };

  const handleCustomUpdate = async () => {
    if (hasUpdate()) {
      await startUpdateWithMessage(
        'Important Update',
        'This update contains critical security fixes. Please update now.',
        { forceUpgrade: true }
      );
    } else {
      Alert.alert('No Update', 'No update available');
    }
  };

  const versionInfo = getVersionInfo();

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>App Update System Demo</Text>

      {/* Version Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Version Information</Text>
        <Text style={styles.info}>Current: {versionInfo.currentVersion}</Text>
        {versionInfo.storeVersion && (
          <Text style={styles.info}>Store: {versionInfo.storeVersion}</Text>
        )}
        <Text style={styles.info}>
          Update Available: {hasUpdate() ? 'Yes' : 'No'}
        </Text>
      </View>

      {/* Update Status */}
      {updateInfo && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Update Status</Text>
          <Text style={styles.info}>
            Has Update: {updateInfo.hasUpdate ? 'Yes' : 'No'}
          </Text>
          {updateInfo.hasUpdate && (
            <>
              <Text style={styles.info}>
                Store Version: {updateInfo.storeVersion}
              </Text>
              <Text style={styles.info}>
                Required: {updateInfo.updateRequired ? 'Yes' : 'No'}
              </Text>
            </>
          )}
        </View>
      )}

      {/* Error Display */}
      {error && (
        <View style={styles.errorSection}>
          <Text style={styles.errorTitle}>Error</Text>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>

        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={handleCheckUpdate}
          disabled={isChecking()}
        >
          <Text style={styles.buttonText}>
            {isChecking() ? 'Checking...' : 'Check for Updates'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={handleFlexibleUpdate}
          disabled={!hasUpdate()}
        >
          <Text style={styles.buttonText}>Flexible Update (Android)</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.warningButton]}
          onPress={handleImmediateUpdate}
          disabled={!hasUpdate()}
        >
          <Text style={styles.buttonText}>Immediate Update</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.dangerButton]}
          onPress={handleCustomUpdate}
          disabled={!hasUpdate()}
        >
          <Text style={styles.buttonText}>Custom Force Update</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.infoButton]}
          onPress={() => setShowModal(true)}
        >
          <Text style={styles.buttonText}>Show Custom Modal</Text>
        </TouchableOpacity>
      </View>

      {/* Custom Modal Example */}
      <AppUpdateModal
        visible={showModal && hasUpdate()}
        onClose={() => setShowModal(false)}
        forceUpdate={false}
        customTitle="Custom Update Title"
        customMessage="This is a custom update message with your own styling and content."
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: '#1F2937',
  },
  section: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#374151',
  },
  info: {
    fontSize: 14,
    marginBottom: 4,
    color: '#6B7280',
  },
  errorSection: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#DC2626',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#3B82F6',
  },
  secondaryButton: {
    backgroundColor: '#6B7280',
  },
  warningButton: {
    backgroundColor: '#F59E0B',
  },
  dangerButton: {
    backgroundColor: '#EF4444',
  },
  infoButton: {
    backgroundColor: '#8B5CF6',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
