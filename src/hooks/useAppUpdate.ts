import { useAppUpdate as useAppUpdateContext } from '@/context/AppUpdateContext';
import { UpdateOptions } from '@/services/AppUpdateService';
import { IAUUpdateKind } from 'sp-react-native-in-app-updates';
import { Platform } from 'react-native';

/**
 * Hook for app update functionality with convenient methods
 */
export const useAppUpdate = () => {
  const context = useAppUpdateContext();

  /**
   * Check for updates with optional force refresh
   */
  const checkForUpdate = async (force: boolean = false) => {
    if (force || !context.updateInfo) {
      await context.checkForUpdate();
    }
    return context.updateInfo;
  };

  /**
   * Start flexible update (Android) or show alert (iOS)
   */
  const startFlexibleUpdate = async (customOptions?: Partial<UpdateOptions>) => {
    const options: UpdateOptions = {
      ...customOptions,
      ...(Platform.OS === 'android' && { updateType: IAUUpdateKind.FLEXIBLE }),
    };
    await context.startUpdate(options);
  };

  /**
   * Start immediate update (Android) or show alert with force upgrade (iOS)
   */
  const startImmediateUpdate = async (customOptions?: Partial<UpdateOptions>) => {
    const options: UpdateOptions = {
      ...customOptions,
      ...(Platform.OS === 'android' 
        ? { updateType: IAUUpdateKind.IMMEDIATE }
        : { forceUpgrade: true }
      ),
    };
    await context.startUpdate(options);
  };

  /**
   * Start update with custom message
   */
  const startUpdateWithMessage = async (
    title: string,
    message: string,
    customOptions?: Partial<UpdateOptions>
  ) => {
    const options: UpdateOptions = {
      title,
      message,
      ...customOptions,
    };
    await context.startUpdate(options);
  };

  /**
   * Check if update is available
   */
  const hasUpdate = (): boolean => {
    return context.updateInfo?.hasUpdate || false;
  };

  /**
   * Check if update is required/mandatory
   */
  const isUpdateRequired = (): boolean => {
    return context.updateInfo?.updateRequired || false;
  };

  /**
   * Get current and store versions
   */
  const getVersionInfo = () => {
    return {
      currentVersion: context.updateInfo?.currentVersion,
      storeVersion: context.updateInfo?.storeVersion,
    };
  };

  /**
   * Get download progress (Android only)
   */
  const getDownloadProgress = (): number => {
    return context.downloadProgress;
  };

  /**
   * Get install status (Android only)
   */
  const getInstallStatus = (): string | null => {
    return context.installStatus;
  };

  /**
   * Check if currently checking for updates
   */
  const isChecking = (): boolean => {
    return context.isChecking;
  };

  /**
   * Get any error that occurred
   */
  const getError = (): string | null => {
    return context.error;
  };

  return {
    // State
    updateInfo: context.updateInfo,
    isChecking: context.isChecking,
    error: context.error,
    downloadProgress: context.downloadProgress,
    installStatus: context.installStatus,

    // Actions
    checkForUpdate,
    startUpdate: context.startUpdate,
    startFlexibleUpdate,
    startImmediateUpdate,
    startUpdateWithMessage,
    dismissUpdate: context.dismissUpdate,
    installUpdate: context.installUpdate,

    // Helpers
    hasUpdate,
    isUpdateRequired,
    getVersionInfo,
    getDownloadProgress,
    getInstallStatus,
    isChecking,
    getError,
  };
};
