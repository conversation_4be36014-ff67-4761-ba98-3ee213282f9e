# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native build directories (these can be huge)
android/build/
android/app/build/
android/.gradle/
ios/build/
ios/Pods/
ios/DerivedData/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# apk and ipa files
*.apk
*.aab
*.ipa

# Development mock files (not needed for production builds)
react-native-device-info.js
sp-react-native-in-app-updates.js

# Documentation and development files
docs/
*.md
README*

# Git and version control
.git/
.gitignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Cache directories
.cache/
.tmp/
tmp/

# Test files
__tests__/
*.test.*
*.spec.*

# Firebase admin SDK (contains sensitive data and not needed for mobile builds)
*firebase-adminsdk*.json

# Package lock files (EAS will regenerate these)
package-lock.json
yarn.lock

# Project configuration files that might not be needed
project.inlang/

# Source maps and build artifacts
*.map
*.d.ts.map

# Large assets that might not be needed (review these)
# Uncomment if these are not essential for the app
# assets/animations/
# assets/sounds/

# Temporary files and backups
*.tmp
*.bak
*.backup
*~

# OS generated files
Thumbs.db
ehthumbs.db