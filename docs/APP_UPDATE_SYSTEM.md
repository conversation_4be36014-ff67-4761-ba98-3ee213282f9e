# App Update System Documentation

This document explains how to use the app update system implemented in the Wodaabe Stays app.

## Overview

The app update system provides cross-platform update notifications and handling:
- **Android**: Uses Google Play's in-app update API for seamless updates
- **iOS**: Uses App Store API with native alerts and App Store redirection

## Architecture

### Core Components

1. **AppUpdateService** (`src/services/AppUpdateService.ts`)
   - Wraps `sp-react-native-in-app-updates` library
   - Provides unified API for both platforms

2. **AppUpdateContext** (`src/context/AppUpdateContext.tsx`)
   - Global state management
   - Automatic update checking on app launch/resume
   - Background update monitoring

3. **useAppUpdate Hook** (`src/hooks/useAppUpdate.ts`)
   - Convenient hook for components
   - Helper methods for common update scenarios

4. **AppUpdateModal** (`src/components/AppUpdateModal.tsx`)
   - Optional custom modal component
   - Consistent UI across platforms

## Basic Usage

### 1. Using the Hook

```typescript
import { useAppUpdate } from '@/hooks/useAppUpdate';

function MyComponent() {
  const { 
    hasUpdate, 
    checkForUpdate, 
    startFlexibleUpdate,
    updateInfo 
  } = useAppUpdate();

  const handleCheckUpdate = async () => {
    await checkForUpdate(true); // Force check
    
    if (hasUpdate()) {
      await startFlexibleUpdate();
    }
  };

  return (
    <TouchableOpacity onPress={handleCheckUpdate}>
      <Text>Check for Updates</Text>
    </TouchableOpacity>
  );
}
```

### 2. Using the Settings Component

```typescript
import { UpdateSettingsSection } from '@/components/UpdateSettingsSection';

function SettingsScreen() {
  return (
    <ScrollView>
      <UpdateSettingsSection />
    </ScrollView>
  );
}
```

### 3. Using the Custom Modal

```typescript
import { AppUpdateModal } from '@/components/AppUpdateModal';
import { useAppUpdate } from '@/hooks/useAppUpdate';

function MyScreen() {
  const { hasUpdate } = useAppUpdate();
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <AppUpdateModal
        visible={showModal && hasUpdate()}
        onClose={() => setShowModal(false)}
        forceUpdate={false}
        customTitle="Update Available"
        customMessage="Please update to get the latest features."
      />
    </>
  );
}
```

## Update Types

### Android Update Types

1. **Flexible Update** (Recommended)
   - Downloads in background
   - User can continue using app
   - Prompts to restart when ready

2. **Immediate Update**
   - Blocks app usage during update
   - Forces immediate installation
   - Use for critical updates only

### iOS Update Behavior

- Shows native iOS alert
- Redirects to App Store
- User manually updates and returns

## Configuration

### AppUpdateProvider Options

```typescript
<AppUpdateProvider
  checkOnLaunch={true}        // Check when app starts
  checkOnResume={true}        // Check when app resumes
  checkInterval={24 * 60 * 60 * 1000} // Check frequency (24 hours)
>
  {/* Your app */}
</AppUpdateProvider>
```

### Update Options

```typescript
const updateOptions = {
  // Android options
  updateType: IAUUpdateKind.FLEXIBLE, // or IMMEDIATE
  
  // iOS options
  title: 'Update Available',
  message: 'A new version is available',
  buttonUpgradeText: 'Update',
  buttonCancelText: 'Later',
  forceUpgrade: false,
  country: 'US', // Optional country filter
};

await startUpdate(updateOptions);
```

## Testing

### Android Testing

1. Create two APK versions (e.g., 1.0.0 and 1.0.1)
2. Enable Internal App Sharing in Google Play Console
3. Upload both versions to Internal App Sharing
4. Install lower version on device
5. Open higher version link (don't install)
6. Verify "UPDATE" button appears
7. Test your app's update flow

### iOS Testing

1. Ensure app is published on App Store
2. Test with lower version number
3. Verify iTunes Search API returns correct data
4. Test App Store redirection

## Error Handling

The system handles various error scenarios:

```typescript
const { error, isChecking } = useAppUpdate();

if (error) {
  console.error('Update error:', error);
  // Show user-friendly error message
}

if (isChecking) {
  // Show loading indicator
}
```

## Platform-Specific Notes

### Android
- Requires Google Play Store distribution
- Works with release builds only (not debug)
- Supports download progress tracking
- Can install updates automatically

### iOS
- Uses iTunes Search API
- Requires App Store distribution
- Shows native iOS alerts
- Redirects to App Store for manual update

## Best Practices

1. **Check Frequency**: Don't check too often (recommended: 24 hours)
2. **User Experience**: Use flexible updates for non-critical updates
3. **Force Updates**: Only for security or breaking changes
4. **Error Handling**: Always handle network and API errors gracefully
5. **Testing**: Test thoroughly on both platforms before release

## Troubleshooting

### Common Issues

1. **Android updates not working**
   - Ensure using release build
   - Verify Google Play distribution
   - Check Internal App Sharing setup

2. **iOS updates not detected**
   - Verify bundle identifier matches App Store
   - Check iTunes Search API response
   - Ensure app is published

3. **Version comparison issues**
   - Use semantic versioning (1.0.0, 1.0.1, etc.)
   - Ensure version numbers are properly incremented

### Debug Logging

Enable debug mode for detailed logging:

```typescript
const appUpdateService = new AppUpdateService(true); // Enable debug
```

## Dependencies

- `sp-react-native-in-app-updates`: Core update functionality
- `expo-constants`: Version information
- `expo-linking`: App Store redirection (iOS)
- `react-native-device-info`: Device information (handled by sp-react-native-in-app-updates)
